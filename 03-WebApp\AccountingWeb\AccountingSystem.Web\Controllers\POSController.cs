using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AccountingSystem.Services;
using AccountingSystem.Web.Models;
using AccountingSystem.Models;
using System.Security.Claims;

namespace AccountingSystem.Web.Controllers
{
    [Authorize]
    public class POSController : Controller
    {
        private readonly IPOSService _posService;
        private readonly ILogger<POSController> _logger;

        public POSController(IPOSService posService, ILogger<POSController> logger)
        {
            _posService = posService;
            _logger = logger;
        }

        [HttpGet]
        [AllowAnonymous]
        public IActionResult Test()
        {
            var isAuthenticated = User.Identity?.IsAuthenticated ?? false;
            var username = User.Identity?.Name ?? "Not authenticated";
            return Content($"POS Controller is working! User: {username}, Authenticated: {isAuthenticated}");
        }

        public async Task<IActionResult> Index()
        {
            _logger.LogInformation("POS Index action called for user: {Username}", User.Identity?.Name);

            try
            {
                var username = User.FindFirst(ClaimTypes.Name)?.Value ?? "admin";
                _logger.LogInformation("Processing POS Index for username: {Username}", username);

                // Get user authorization information
                var userAuth = await _posService.GetUserAuthorizationAsync(username);
                var currentInvoice = await _posService.GetCurrentInvoiceAsync();
                var stores = await _posService.GetStoresAsync();
                var customers = await _posService.GetCustomersAsync();
                var favoriteItems = await _posService.GetFavoriteItemsAsync(username);
                var settings = await _posService.GetPOSSettingsAsync();

                // Check session validation based on user role
                bool hasActiveSession;
                POSSession? activeSession;

                if (userAuth.IsAdmin)
                {
                    // Admin can use any open session
                    hasActiveSession = await _posService.ValidateActiveSessionAsync();
                    activeSession = await _posService.GetActiveSessionAsync();
                }
                else
                {
                    var userStore = await _posService.GetUserDefaultStoreAsync(username);
                    if (userStore == null)
                    {
                        _logger.LogWarning("User {Username} does not have a default store assigned.", username);
                        TempData["Error"] = "ليس لديك متجر افتراضي معين. يرجى الاتصال بالمسؤول.";
                        return RedirectToAction("Index", "Home");
                    }
                    // Non-admin users are restricted to their assigned store
                    hasActiveSession = await _posService.ValidateActiveSessionAsync(username, userStore.StoreName);
                    activeSession = await _posService.GetActiveSessionAsync(username, userStore.StoreName);
                }

                // Get invoice items
                var invoiceItems = currentInvoice != null ? await _posService.GetInvoiceItemsAsync(currentInvoice.TrxNo) : new List<POSInvoiceItem>();

                // Get customer information
                string customerName = "";
                string customerPhone = "";
                long? customerId = null;

                if (currentInvoice != null && currentInvoice.PartnerNo.HasValue)
                {
                    var customer = customers.FirstOrDefault(c => c.AccountCode == currentInvoice.PartnerNo.Value.ToString());
                    if (customer != null)
                    {
                        customerName = customer.AccountName ?? "";
                        customerPhone = currentInvoice.PartnerPhoneNo ?? "";
                        customerId = long.Parse(customer.AccountCode);
                    }
                }

                var viewModel = new POSViewModel
                {
                    InvoiceNo = currentInvoice != null ? currentInvoice.TrxNo : 0,
                    Store = currentInvoice?.Store ?? stores.FirstOrDefault()?.StoreName ?? "",
                    Cashier = currentInvoice?.Cashier ?? username,
                    CustomerName = customerName,
                    CustomerPhone = customerPhone,
                    CustomerId = customerId,
                    TotalAmount = currentInvoice?.TrxTotal ?? 0,
                    VATAmount = currentInvoice?.TrxVAT ?? 0,
                    DiscountAmount = currentInvoice?.TrxDiscountValue ?? 0,
                    NetAmount = currentInvoice?.TrxNetAmount ?? 0,
                    ItemCount = invoiceItems.Count,
                    TotalQuantity = invoiceItems.Sum(i => i.TrxQTY),
                    Items = invoiceItems.Select(i => new POSInvoiceItemViewModel
                    {
                        LineSN = i.LineSN,
                        ItemNo = i.ItemNo,
                        ItemDescription = _posService.GetItemDescriptionAsync(i.ItemNo).GetAwaiter().GetResult(),
                        Quantity = i.TrxQTY,
                        UofM = i.UofM ?? "",
                        UnitPrice = i.UnitPrice,
                        LineAmount = i.LineAmount,
                        VATAmount = i.VATAmount
                    }).ToList(),
                    FavoriteItems = favoriteItems,
                    Stores = stores,
                    Customers = customers,
                    Settings = settings,
                    HasActiveSession = hasActiveSession,
                    ActiveSession = activeSession,
                    // User authorization properties
                    CanChangeCustomer = userAuth.IsAdmin || userAuth.CustomerChange,
                    CanChangePrice = userAuth.IsAdmin || userAuth.ChangeInvoicePrice,
                    MaxDiscountPercent = userAuth.MaxDiscountPercent ?? 0,
                    DefaultCustomerId = userAuth.DefaultCustomer,
                    DefaultStore = userAuth.DefaultStore
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading POS screen");
                TempData["Error"] = "حدث خطأ أثناء تحميل شاشة نقاط البيع";
                return RedirectToAction("Index", "Home");
            }
        }

        [HttpPost]
        public async Task<IActionResult> CreateNewInvoice([FromBody] string store)
        {
            try
            {
                var username = User.FindFirst(ClaimTypes.Name)?.Value ?? "admin";
                
                // Validate active session before creating invoice
                if (!await _posService.ValidateActiveSessionAsync(username, store))
                {
                    return Json(new { 
                        success = false, 
                        message = $"لا يوجد جلسة نشطة للمستخدم {username} في المتجر {store}. يرجى فتح جلسة جديدة أولاً.",
                        requiresSession = true
                    });
                }

                var invoice = await _posService.CreateNewInvoiceAsync(username, store);

                return Json(new { success = true, invoiceNo = invoice.TrxNo });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating new invoice");
                return Json(new { success = false, message = "حدث خطأ أثناء إنشاء الفاتورة الجديدة" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> AddItem([FromBody] AddItemRequest request)
        {
            try
            {
                var username = User.FindFirst(ClaimTypes.Name)?.Value ?? "admin";
                
                // Validate active session
                if (!await _posService.ValidateActiveSessionAsync(username, request.Store))
                {
                    return Json(new { 
                        success = false, 
                        message = "لا يوجد جلسة نشطة لنقاط البيع",
                        requiresSession = true
                    });
                }

                var item = await _posService.AddItemToInvoiceAsync(
                    request.InvoiceNo, 
                    request.ItemNo, 
                    request.Quantity, 
                    request.UnitPrice, 
                    username, 
                    request.Store
                );

                return Json(new { success = true, item = item });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding item to invoice");
                return Json(new { success = false, message = "حدث خطأ أثناء إضافة الصنف" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> UpdateItem([FromBody] UpdateItemRequest request)
        {
            try
            {
                var username = User.FindFirst(ClaimTypes.Name)?.Value ?? "admin";
                
                var success = await _posService.UpdateInvoiceItemAsync(
                    request.InvoiceNo, 
                    request.LineSN, 
                    request.Quantity, 
                    request.UnitPrice, 
                    username
                );

                return Json(new { success = success, message = success ? "تم تحديث الصنف بنجاح" : "فشل في تحديث الصنف" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating item");
                return Json(new { success = false, message = "حدث خطأ أثناء تحديث الصنف" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> DeleteItem([FromBody] DeleteItemRequest request)
        {
            try
            {
                var username = User.FindFirst(ClaimTypes.Name)?.Value ?? "admin";
                
                var success = await _posService.DeleteInvoiceItemAsync(
                    request.InvoiceNo, 
                    request.LineSN, 
                    username
                );

                return Json(new { success = success, message = success ? "تم حذف الصنف بنجاح" : "فشل في حذف الصنف" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting item");
                return Json(new { success = false, message = "حدث خطأ أثناء حذف الصنف" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> SaveAndCompleteInvoice([FromBody] SaveAndCompleteInvoiceRequest request)
        {
            try
            {
                var username = User.FindFirst(ClaimTypes.Name)?.Value ?? "admin";
                
                // First save the invoice (only takes invoiceNo and username)
                await _posService.SaveInvoiceAsync(request.InvoiceNo, username);

                // Then complete the transaction with payments
                var invoice = await _posService.CompleteInvoiceAsync(
                    request.InvoiceNo, 
                    request.CashAmount, 
                    request.CardAmount, 
                    username
                );

                return Json(new { success = true, invoice = invoice });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving and completing invoice");
                return Json(new { success = false, message = "حدث خطأ أثناء حفظ وإتمام البيع" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> ClearInvoiceItems([FromBody] ClearInvoiceRequest request)
        {
            try
            {
                await _posService.ClearInvoiceItemsAsync(request.InvoiceNo);
                return Json(new { success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing invoice items");
                return Json(new { success = false, message = "حدث خطأ أثناء مسح أصناف الفاتورة" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> SearchItems([FromBody] SearchItemsRequest request)
        {
            try
            {
                var items = await _posService.SearchItemsAsync(request.SearchTerm);
                
                var result = items.Select(item => new
                {
                    itemNo = item.ItemNo,
                    description = item.ItemDescription ?? "",
                    unitPrice = item.UnitSalesPrice,
                    uofM = item.SalesUofM ?? ""
                }).ToList();

                return Json(new { success = true, items = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching items");
                return Json(new { success = false, message = "حدث خطأ أثناء البحث عن الأصناف" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> ProcessBarcode([FromBody] ProcessBarcodeRequest request)
        {
            try
            {
                var username = User.FindFirst(ClaimTypes.Name)?.Value ?? "admin";
                
                var result = await _posService.ProcessBarcodeAsync(
                    request.Barcode, 
                    request.InvoiceNo, 
                    request.Store, 
                    username
                );

                if (result.Success)
                {
                    // Add the item to the invoice
                    var item = await _posService.AddItemToInvoiceAsync(
                        request.InvoiceNo,
                        result.Item.ItemNo ?? 0,
                        result.Quantity,
                        result.UnitPrice,
                        username,
                        request.Store
                    );

                    return Json(new { success = true, message = "تم إضافة الصنف بنجاح" });
                }
                else
                {
                    return Json(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing barcode");
                return Json(new { success = false, message = "حدث خطأ أثناء معالجة الباركود" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> SearchCustomerByPhone([FromBody] SearchCustomerByPhoneRequest request)
        {
            try
            {
                var customers = await _posService.GetCustomersAsync();
                // Since ChartOfAccount doesn't have Phone/Mobile, we'll search by account name for now
                var customer = customers.FirstOrDefault(c => 
                    c.AccountName.Contains(request.Phone, StringComparison.OrdinalIgnoreCase));

                if (customer != null)
                {
                    return Json(new { 
                        success = true, 
                        customer = new
                        {
                            accountCode = customer.AccountCode,
                            accountName = customer.AccountName,
                            phone = request.Phone // Use the searched phone number
                        }
                    });
                }
                else
                {
                    return Json(new { success = false, message = "لم يتم العثور على العميل" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching customer by phone");
                return Json(new { success = false, message = "حدث خطأ أثناء البحث عن العميل" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> SearchCustomers([FromBody] SearchCustomersRequest request)
        {
            try
            {
                var customers = await _posService.GetCustomersAsync();
                var filteredCustomers = customers.Where(c => 
                    c.AccountName.Contains(request.SearchTerm, StringComparison.OrdinalIgnoreCase)
                ).Take(20).ToList();

                var result = filteredCustomers.Select(c => new
                {
                    accountCode = c.AccountCode,
                    accountName = c.AccountName,
                    phone = "" // ChartOfAccount doesn't have phone property
                }).ToList();

                return Json(new { success = true, customers = result });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching customers");
                return Json(new { success = false, message = "حدث خطأ أثناء البحث عن العملاء" });
            }
        }

        [HttpPost]
        public async Task<IActionResult> ChangeStore([FromBody] ChangeStoreRequest request)
        {
            try
            {
                var username = User.FindFirst(ClaimTypes.Name)?.Value ?? "admin";
                
                // Validate active session for the new store
                if (!await _posService.ValidateActiveSessionAsync(username, request.Store))
                {
                    return Json(new { 
                        success = false, 
                        message = $"لا يوجد جلسة نشطة للمستخدم {username} في المتجر {request.Store}",
                        requiresSession = true
                    });
                }

                return Json(new { success = true, message = "تم تغيير المتجر بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error changing store");
                return Json(new { success = false, message = "حدث خطأ أثناء تغيير المتجر" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetStores()
        {
            try
            {
                var stores = await _posService.GetStoresAsync();
                return Json(new { success = true, stores = stores });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting stores");
                return Json(new { success = false, message = "حدث خطأ أثناء جلب المتاجر" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetCustomers()
        {
            try
            {
                var customers = await _posService.GetCustomersAsync();
                return Json(new { success = true, customers = customers });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting customers");
                return Json(new { success = false, message = "حدث خطأ أثناء جلب العملاء" });
            }
        }

        [HttpGet]
        public async Task<IActionResult> Receipt(int invoiceNo)
        {
            try
            {
                var invoice = await _posService.GetInvoiceByNumberAsync(invoiceNo);
                if (invoice == null)
                {
                    TempData["Error"] = "لم يتم العثور على الفاتورة";
                    return RedirectToAction("Index");
                }

                var invoiceItems = await _posService.GetInvoiceItemsAsync(invoice.TrxNo);
                var payments = await _posService.GetInvoicePaymentsAsync(invoiceNo);

                var viewModel = new ReceiptViewModel
                {
                    Invoice = invoice,
                    Items = invoiceItems,
                    Payments = payments
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating receipt");
                TempData["Error"] = "حدث خطأ أثناء إنشاء الإيصال";
                return RedirectToAction("Index");
            }
        }

        [HttpGet]
        [Route("POS/ThermalReceipt/{id:long}")]
        public async Task<IActionResult> ThermalReceipt(long id)
        {
            try
            {
                _logger.LogInformation("ThermalReceipt requested for invoice: {InvoiceNo}", id);
                
                var invoice = await _posService.GetInvoiceByNumberAsync(id);
                if (invoice == null)
                {
                    _logger.LogWarning("Invoice {InvoiceNo} not found", id);
                    return Content($@"
                        <html dir='rtl'><body style='text-align:center;padding:20px;font-family:Arial;'>
                        <h3>لم يتم العثور على الفاتورة رقم {id}</h3>
                        </body></html>", "text/html");
                }

                _logger.LogInformation("Invoice found: {InvoiceNo}, getting items...", id);
                var invoiceItems = await _posService.GetInvoiceItemsAsync(invoice.TrxNo);
                _logger.LogInformation("Found {ItemCount} items for invoice {InvoiceNo}", invoiceItems.Count, id);
                
                var payments = await _posService.GetInvoicePaymentsAsync(id);
                _logger.LogInformation("Found {PaymentCount} payments for invoice {InvoiceNo}", payments.Count, id);

                var viewModel = new ReceiptViewModel
                {
                    Invoice = invoice,
                    Items = invoiceItems,
                    Payments = payments
                };

                _logger.LogInformation("Rendering ThermalReceipt view for invoice: {InvoiceNo}", id);
                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in ThermalReceipt for invoice {InvoiceNo}: {ErrorMessage}", id, ex.Message);
                return Content($@"
                    <html dir='rtl'><body style='text-align:center;padding:20px;font-family:Arial;'>
                    <h3>حدث خطأ أثناء إنشاء الإيصال الحراري</h3>
                    <p>رقم الفاتورة: {id}</p>
                    <p>الخطأ: {ex.Message}</p>
                    </body></html>", "text/html");
            }
        }

        [HttpGet]
        [Route("POS/TestReceipt/{id:long}")]
        public IActionResult TestReceipt(long id)
        {
            _logger.LogInformation("TestReceipt called with invoiceNo: {InvoiceNo}", id);
            return Content($@"
                <html dir='rtl'>
                <body style='text-align:center;padding:20px;font-family:Arial;'>
                <h3>Test Receipt for Invoice: {id}</h3>
                <p>This is a test to verify routing is working</p>
                </body>
                </html>", "text/html");
        }
    }

    // Request models
    public class AddItemRequest
    {
        public int InvoiceNo { get; set; }
        public long ItemNo { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public string Store { get; set; } = "";
    }

    public class UpdateItemRequest
    {
        public int InvoiceNo { get; set; }
        public int LineSN { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
    }

    public class DeleteItemRequest
    {
        public int InvoiceNo { get; set; }
        public int LineSN { get; set; }
    }

    public class SaveInvoiceRequest
    {
        public int InvoiceNo { get; set; }
        public long? CustomerId { get; set; }
        public string CustomerName { get; set; } = "";
        public string CustomerPhone { get; set; } = "";
        public decimal DiscountPercent { get; set; }
    }

    public class CompleteTransactionRequest
    {
        public int InvoiceNo { get; set; }
        public decimal CashAmount { get; set; }
        public decimal CardAmount { get; set; }
    }

    public class ClearInvoiceRequest
    {
        public int InvoiceNo { get; set; }
    }

    public class SearchItemsRequest
    {
        public string SearchTerm { get; set; } = "";
    }

    public class ProcessBarcodeRequest
    {
        public string Barcode { get; set; } = "";
        public int InvoiceNo { get; set; }
        public string Store { get; set; } = "";
    }

    public class SearchCustomerByPhoneRequest
    {
        public string Phone { get; set; } = "";
    }

    public class SearchCustomersRequest
    {
        public string SearchTerm { get; set; } = "";
    }

    public class ChangeStoreRequest
    {
        public string Store { get; set; } = "";
    }

    public class SaveAndCompleteInvoiceRequest
    {
        public int InvoiceNo { get; set; }
        public long? CustomerId { get; set; }
        public string CustomerName { get; set; } = "";
        public string CustomerPhone { get; set; } = "";
        public decimal DiscountPercent { get; set; }
        public decimal CashAmount { get; set; }
        public decimal CardAmount { get; set; }
    }
} 
