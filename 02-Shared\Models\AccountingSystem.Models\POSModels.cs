using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace AccountingSystem.Models
{
    /// <summary>
    /// POS Invoice - matches tblStockMovHeader structure
    /// </summary>
    public class POSInvoice
    {
        [Required]
        [Key]
        public int TrxNo { get; set; } // Invoice number

        [Required]
        [StringLength(50)]
        public string TrxType { get; set; } = "مبيعات";

        public DateTime TrxDate { get; set; } = DateTime.Now;

        [StringLength(50)]
        public string? Store { get; set; }

        [StringLength(50)]
        public string? Cashier { get; set; }

        public int? PartnerNo { get; set; } // Customer account code

        [StringLength(100)]
        public string? PartnerName { get; set; }

        [StringLength(20)]
        public string? PartnerPhoneNo { get; set; }

        [StringLength(50)]
        public string? PaymentMethod { get; set; }

        [StringLength(100)]
        public string? PartnerReference { get; set; }

        [StringLength(500)]
        public string? TrxNote { get; set; }

        public decimal TrxVAT { get; set; } = 0;

        public decimal TrxTotal { get; set; } = 0;

        public decimal TrxDiscount { get; set; } = 0;

        public decimal TrxDiscountValue { get; set; } = 0;

        public decimal TrxNetAmount { get; set; } = 0;

        [StringLength(10)]
        public string? ReadyForUse { get; set; }

        public bool VOIDSTTS { get; set; } = false;

        public byte[]? QRCodeImage { get; set; }

        [StringLength(50)]
        public string? CreatedBy { get; set; }
        public DateTime? CreatedOn { get; set; }
        [StringLength(50)]
        public string? ModifiedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }
    }

    /// <summary>
    /// POS Invoice Item - matches tblStockMovement structure
    /// </summary>
    public class POSInvoiceItem
    {
        public int DocNo { get; set; } // Invoice number

        public int LineSN { get; set; } // Line sequence number

        public DateTime TrxDate { get; set; } = DateTime.Now;

        public long ItemNo { get; set; }

        [StringLength(50)]
        public string? Store { get; set; }

        public decimal TrxQTY { get; set; } = 0;

        [Required]
        [StringLength(50)]
        public string TrxType { get; set; } = "مبيعات";

        public decimal UnitPrice { get; set; } = 0;

        [StringLength(50)]
        public string? UofM { get; set; }

        public decimal UofMConversion { get; set; } = 1;

        public decimal VATAmount { get; set; } = 0;

        public decimal LineAmount { get; set; } = 0;

        [StringLength(50)]
        public string? CreatedBy { get; set; }
        public DateTime? CreatedOn { get; set; }
        [StringLength(50)]
        public string? ModifiedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }
    }

    /// <summary>
    /// POS Payment - matches tblPayMethodTrx structure
    /// </summary>
    public class POSPayment
    {
        public int TrxNo { get; set; } // Invoice number

        public int Pay_mthd { get; set; } // Payment method ID (1=Cash, 2=Card, etc.)

        public decimal Pay_amnt { get; set; } = 0;

        [StringLength(50)]
        public string? CreatedBy { get; set; }
        public DateTime? CreatedOn { get; set; }
    }

    /// <summary>
    /// Item entity - matches tblItems structure
    /// </summary>
    public class Item
    {
        [Key]
        public long SN { get; set; }

        public long? ItemNo { get; set; }

        public string? ItemDescription { get; set; }
        
        public string? ItemDescription2 { get; set; }

        [StringLength(50)]
        public string? Barcode { get; set; }

        public int? CategoryId { get; set; }

        [StringLength(50)]
        public string? UofM { get; set; }

        public bool? EnableSN { get; set; }
        
        public bool? NegativeEnable { get; set; }

        public decimal? UnitSalesPrice { get; set; }

        [StringLength(50)]
        public string? SalesUofM { get; set; }

        public decimal? UnitPurchasePrice { get; set; }

        [StringLength(50)]
        public string? ItemType { get; set; }
        
        [StringLength(50)]
        public string? AUofM { get; set; }
        public decimal? AUofMX { get; set; }
        public decimal? AUofM_Price { get; set; }

        [StringLength(50)]
        public string? AUofM2 { get; set; }
        public decimal? AUofMX2 { get; set; }
        public decimal? AUofM2_Price { get; set; }

        [StringLength(50)]
        public string? AUofM3 { get; set; }
        public decimal? AUofMX3 { get; set; }
        public decimal? AUofM3_Price { get; set; }
        
        public string? CreatedBy { get; set; }
        public DateTime? CreatedOn { get; set; }
        
        public string? ModifiedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }

        public decimal? Tax_Percent { get; set; }
        
        [StringLength(50)]
        public string? Brand { get; set; }

        public byte? Status { get; set; }

        public byte[]? Photo { get; set; }
        
        public string? Notes { get; set; }

        [StringLength(50)]
        public string? Shop { get; set; }
        
        public decimal? UnitAveragePrice { get; set; }

        public string? OldCode { get; set; }
    }

    /// <summary>
    /// Store entity - matches tblShops structure now
    /// </summary>
    [Table("tblShops")]
    public class Store
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        [Required]
        public int SN { get; set; }

        [Required]
        [StringLength(100)]
        [Column("Shop_Text")]
        public string StoreName { get; set; } = string.Empty;

        [StringLength(50)]
        public string? CreatedBy { get; set; }
        public DateTime? CreatedOn { get; set; }
        [StringLength(50)]
        public string? ModifiedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }
    }

    /// <summary>
    /// User POS Items - matches tblUserPOSItems structure
    /// </summary>
    public class UserPOSItem
    {
        [Required]
        [StringLength(50)]
        public string Username { get; set; } = string.Empty;

        [Required]
        public long ItemNo { get; set; }
    }

    /// <summary>
    /// Barcode Settings - matches tblBarcodeSettings structure
    /// </summary>
    public class BarcodeSettings
    {
        [Key]
        public int ID { get; set; }

        [Required]
        [StringLength(50)]
        public string Shop { get; set; } = string.Empty;

        [StringLength(50)]
        public string? BarcodeType { get; set; }

        public bool EnableEmbeddedWeight { get; set; }

        [StringLength(50)]
        public string? EmbeddedFormat { get; set; }

        public int? WeightDivisor { get; set; }
        
        public int? CurrencyDivisor { get; set; }

        [StringLength(255)]
        public string? Notes { get; set; }

        [StringLength(50)]
        public string? CreatedBy { get; set; }

        public DateTime? CreatedOn { get; set; }

        [StringLength(50)]
        public string? ModifiedBy { get; set; }

        public DateTime? ModifiedOn { get; set; }

        [StringLength(50)]
        public string? Barcode { get; set; }

        [StringLength(50)]
        public string? Weight { get; set; }

        [StringLength(50)]
        public string? FixedCode { get; set; }
    }

    public class UserAuthorization
    {
        public bool IsAdmin { get; set; }
        public bool CustomerChange { get; set; }
        public bool ChangeInvoicePrice { get; set; }
        public decimal? MaxDiscountPercent { get; set; }
        public long? DefaultCustomer { get; set; }
        public string? DefaultStore { get; set; }
    }

    public class BarcodeProcessResult
    {
        public bool Success { get; set; }
        public Item? Item { get; set; }
        public decimal Quantity { get; set; }
        public decimal UnitPrice { get; set; }
        public string Message { get; set; } = "";
    }
}
