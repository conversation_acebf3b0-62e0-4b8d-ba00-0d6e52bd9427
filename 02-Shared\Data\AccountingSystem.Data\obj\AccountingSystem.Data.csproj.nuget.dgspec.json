{"format": 1, "restore": {"D:\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Data\\AccountingSystem.Data\\AccountingSystem.Data.csproj": {}}, "projects": {"D:\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Data\\AccountingSystem.Data\\AccountingSystem.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Data\\AccountingSystem.Data\\AccountingSystem.Data.csproj", "projectName": "AccountingSystem.Data", "projectPath": "D:\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Data\\AccountingSystem.Data\\AccountingSystem.Data.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Data\\AccountingSystem.Data\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Models\\AccountingSystem.Models\\AccountingSystem.Models.csproj": {"projectPath": "D:\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Models\\AccountingSystem.Models\\AccountingSystem.Models.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}, "D:\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Models\\AccountingSystem.Models\\AccountingSystem.Models.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Models\\AccountingSystem.Models\\AccountingSystem.Models.csproj", "projectName": "AccountingSystem.Models", "projectPath": "D:\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Models\\AccountingSystem.Models\\AccountingSystem.Models.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\AlsultanWeb\\AccountingWebApp\\02-Shared\\Models\\AccountingSystem.Models\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}}