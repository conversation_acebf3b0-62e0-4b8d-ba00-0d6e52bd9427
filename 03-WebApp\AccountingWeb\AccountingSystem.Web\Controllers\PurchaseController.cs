using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;

namespace AccountingSystem.Web.Controllers
{
    [Authorize]
    public class PurchaseController : Controller
    {
        public IActionResult Index()
        {
            return View();
        }

        public IActionResult Invoice()
        {
            ViewBag.PageTitle = "فواتير المشتريات";
            ViewBag.Message = "صفحة فواتير المشتريات - قيد التطوير";
            return View();
        }

        public IActionResult Return()
        {
            ViewBag.PageTitle = "مرتجع المشتريات";
            ViewBag.Message = "صفحة مرتجع المشتريات - قيد التطوير";
            return View();
        }

        public IActionResult Create()
        {
            ViewBag.PageTitle = "إنشاء فاتورة مشتريات جديدة";
            return View();
        }

        public IActionResult Edit(int id)
        {
            ViewBag.PageTitle = "تعديل فاتورة المشتريات";
            ViewBag.InvoiceId = id;
            return View();
        }

        public IActionResult Details(int id)
        {
            ViewBag.PageTitle = "تفاصيل فاتورة المشتريات";
            ViewBag.InvoiceId = id;
            return View();
        }
    }
}
