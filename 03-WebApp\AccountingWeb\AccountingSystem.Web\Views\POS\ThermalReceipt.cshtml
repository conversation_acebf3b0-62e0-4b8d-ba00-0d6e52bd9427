@model AccountingSystem.Web.Models.ReceiptViewModel
@{
    ViewData["Title"] = "إيصال الفاتورة";
    Layout = null;
}

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>إيصال الفاتورة رقم @Model.Invoice.TrxNo</title>
    <style>
        @@media print {
            body { margin: 0; padding: 5px; }
            .no-print { display: none !important; }
        }
        
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 10px;
            background-color: white;
            direction: rtl;
            font-size: 12px;
            width: 80mm;
            max-width: 80mm;
        }
        
        .receipt {
            width: 100%;
            background: white;
            padding: 5px;
        }
        
        .header {
            text-align: center;
            border-bottom: 1px solid #000;
            padding-bottom: 10px;
            margin-bottom: 10px;
        }
        
        .logo {
            max-width: 60px;
            height: auto;
            margin-bottom: 5px;
        }
        
        .store-name {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 3px;
        }
        
        .vat-reg {
            font-size: 10px;
            margin-bottom: 2px;
        }
        
        .address {
            font-size: 9px;
            margin-bottom: 5px;
        }
        
        .invoice-info {
            font-size: 10px;
            margin-bottom: 8px;
        }
        
        .invoice-info div {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2px;
        }
        
        .items-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 9px;
            margin-bottom: 10px;
        }
        
        .items-table th,
        .items-table td {
            border-bottom: 1px solid #ddd;
            padding: 3px 2px;
            text-align: right;
        }
        
        .items-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        
        .totals {
            border-top: 1px solid #000;
            padding-top: 5px;
            font-size: 10px;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 2px;
        }
        
        .final-total {
            font-size: 12px;
            font-weight: bold;
            border-top: 1px solid #000;
            padding-top: 3px;
            margin-top: 5px;
        }
        
        .qr-code {
            text-align: center;
            margin: 10px 0;
        }
        
        .qr-code img {
            max-width: 80px;
            height: auto;
        }
        
        .footer {
            text-align: center;
            font-size: 9px;
            margin-top: 10px;
            border-top: 1px solid #ddd;
            padding-top: 5px;
        }
    </style>
</head>
<body onload="window.print();">
    <div class="receipt">
        <!-- Header -->
        <div class="header">
            @await Html.PartialAsync("_ReceiptLogo")
            <div class="store-name">@await GetStoreNameAsync()</div>
            <div class="vat-reg">الرقم الضريبي: @await GetVATRegNoAsync()</div>
            <div class="address">@await GetAddressAsync()</div>
        </div>

        <!-- Invoice Information -->
        <div class="invoice-info">
            <div><span>فاتورة ضريبية مبسطة</span></div>
            <div><span>رقم الفاتورة:</span><span>@Model.Invoice.TrxNo</span></div>
            <div><span>التاريخ:</span><span>@Model.Invoice.TrxDate.ToString("yyyy-MM-dd")</span></div>
            <div><span>الوقت:</span><span>@Model.Invoice.TrxDate.ToString("HH:mm")</span></div>
            <div><span>الكاشير:</span><span>@Model.Invoice.Cashier</span></div>
        </div>

        <!-- Items Table -->
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 40%">البند</th>
                    <th style="width: 15%">الكمية</th>
                    <th style="width: 20%">السعر</th>
                    <th style="width: 25%">المجموع</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model.Items)
                {
                    <tr>
                        <td>@(await GetItemDescriptionAsync(item.ItemNo))</td>
                        <td>@item.TrxQTY.ToString("N0")</td>
                        <td>@item.UnitPrice.ToString("N2")</td>
                        <td>@item.LineAmount.ToString("N2")</td>
                    </tr>
                }
            </tbody>
        </table>

        <!-- Totals -->
        <div class="totals">
            <div class="total-row"><span>المجموع:</span><span>@Model.Invoice.TrxTotal.ToString("N2")</span></div>
            <div class="total-row"><span>الخصم:</span><span>@Model.Invoice.TrxDiscountValue.ToString("N2")</span></div>
            <div class="total-row"><span>الضريبة المضافة (15%):</span><span>@Model.Invoice.TrxVAT.ToString("N2")</span></div>
            <div class="total-row final-total"><span>الإجمالي:</span><span>@Model.Invoice.TrxNetAmount.ToString("N2")</span></div>
        </div>

        <!-- QR Code -->
        @if (Model.Invoice.QRCodeImage != null)
        {
            <div class="qr-code">
                <img src="data:image/png;base64,@Convert.ToBase64String(Model.Invoice.QRCodeImage)" alt="QR Code" />
            </div>
        }

        <!-- Footer -->
        <div class="footer">
            <div>شكراً لزيارتكم</div>
        </div>
    </div>
</body>
</html>

@functions {
    private async Task<string> GetStoreNameAsync()
    {
        // Get from tblConfig
        return "المؤسسة التجارية الحديثة للتجارة";
    }

    private async Task<string> GetVATRegNoAsync()
    {
        // Get from tblConfig.VATRegNo
        return "300656673400003";
    }

    private async Task<string> GetAddressAsync()
    {
        // Get from tblConfig.AddressFooter
        return "جدة - الرياض";
    }

    private async Task<string> GetItemDescriptionAsync(long itemNo)
    {
        return $"صنف رقم {itemNo}";
    }
} 
