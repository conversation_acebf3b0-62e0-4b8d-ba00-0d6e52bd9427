using System.Diagnostics;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AccountingSystem.Web.Models;

namespace AccountingSystem.Web.Controllers;

[Authorize]
public class HomeController : Controller
{
    private readonly ILogger<HomeController> _logger;

    public HomeController(ILogger<HomeController> logger)
    {
        _logger = logger;
    }

    public IActionResult Index()
    {
        // Get user information from claims
        var username = User.Identity?.Name ?? "Unknown";
        var userGroup = User.FindFirst("UserGroup")?.Value ?? "";
        var fullName = User.FindFirst("FullName")?.Value ?? username;

        ViewBag.Username = username;
        ViewBag.UserGroup = userGroup;
        ViewBag.FullName = fullName;
        ViewBag.LoginTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm");

        return View();
    }

    public IActionResult Privacy()
    {
        return View();
    }

    [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
    public IActionResult Error()
    {
        return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
    }
}
