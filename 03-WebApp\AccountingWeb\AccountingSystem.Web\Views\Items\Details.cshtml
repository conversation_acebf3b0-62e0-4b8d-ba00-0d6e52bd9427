@model AccountingSystem.Models.Item

@{
    ViewData["Title"] = "تفاصيل الصنف";
}

<h1>@ViewData["Title"]</h1>

<div>
    <h4>بيانات الصنف</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.ItemNo)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.ItemNo)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.ItemDescription)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.ItemDescription)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.Status)
        </dt>
        <dd class = "col-sm-10">
            @(Model.Status == 1 ? "نشط" : "غير نشط")
        </dd>
        // ... Add other properties to display
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model.SN">تعديل</a> |
    <a asp-action="Index">العودة إلى القائمة</a>
</div> 