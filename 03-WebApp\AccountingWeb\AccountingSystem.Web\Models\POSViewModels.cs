using System.ComponentModel.DataAnnotations;
using AccountingSystem.Models;
using System.Collections.Generic;

namespace AccountingSystem.Web.Models
{
    public class POSViewModel
    {
        public long InvoiceNo { get; set; }
        public string Store { get; set; } = string.Empty;
        public string Cashier { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string CustomerPhone { get; set; } = string.Empty;
        public long? CustomerId { get; set; }
        public decimal TotalAmount { get; set; } = 0;
        public decimal VATAmount { get; set; } = 0;
        public decimal DiscountAmount { get; set; } = 0;
        public decimal NetAmount { get; set; } = 0;
        public int ItemCount { get; set; } = 0;
        public decimal TotalQuantity { get; set; } = 0;
        public List<POSInvoiceItemViewModel> Items { get; set; } = new List<POSInvoiceItemViewModel>();
        public List<Item> FavoriteItems { get; set; } = new List<Item>();
        public List<Store> Stores { get; set; } = new List<Store>();
        public List<ChartOfAccount> Customers { get; set; } = new List<ChartOfAccount>();
        public InvoiceToolSetting? Settings { get; set; }
        
        // Session validation properties
        public bool HasActiveSession { get; set; } = false;
        public POSSession? ActiveSession { get; set; }
        
        // User authorization properties
        public bool CanChangeCustomer { get; set; } = true;
        public bool CanChangePrice { get; set; } = false;
        public decimal MaxDiscountPercent { get; set; } = 0;
        public int? DefaultCustomerId { get; set; }
        public string? DefaultStore { get; set; }
    }

    public class POSInvoiceItemViewModel
    {
        public int LineSN { get; set; }
        public long ItemNo { get; set; }
        public string ItemDescription { get; set; } = string.Empty;
        public decimal Quantity { get; set; }
        public string UofM { get; set; } = string.Empty;
        public decimal UnitPrice { get; set; }
        public decimal LineAmount { get; set; }
        public decimal VATAmount { get; set; }
    }

    public class ReceiptViewModel
    {
        public POSInvoice Invoice { get; set; } = new();
        public List<POSInvoiceItem> Items { get; set; } = new();
        public List<POSPayment> Payments { get; set; } = new();
    }

    public class AddItemViewModel
    {
        [Required(ErrorMessage = "رقم الفاتورة مطلوب")]
        public long InvoiceNo { get; set; }

        [Required(ErrorMessage = "رقم الصنف مطلوب")]
        public long ItemNo { get; set; }

        [Required(ErrorMessage = "الكمية مطلوبة")]
        [Range(0.001, double.MaxValue, ErrorMessage = "الكمية يجب أن تكون أكبر من صفر")]
        public decimal Quantity { get; set; } = 1;

        [Required(ErrorMessage = "سعر الوحدة مطلوب")]
        [Range(0, double.MaxValue, ErrorMessage = "سعر الوحدة يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal UnitPrice { get; set; } = 0;

        public string Store { get; set; } = string.Empty;
    }

    public class UpdateItemViewModel
    {
        [Required(ErrorMessage = "رقم الفاتورة مطلوب")]
        public long InvoiceNo { get; set; }

        [Required(ErrorMessage = "رقم السطر مطلوب")]
        public int LineSN { get; set; }

        [Required(ErrorMessage = "الكمية مطلوبة")]
        [Range(0.001, double.MaxValue, ErrorMessage = "الكمية يجب أن تكون أكبر من صفر")]
        public decimal Quantity { get; set; }

        [Required(ErrorMessage = "سعر الوحدة مطلوب")]
        [Range(0, double.MaxValue, ErrorMessage = "سعر الوحدة يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal UnitPrice { get; set; }
    }

    public class DeleteItemViewModel
    {
        [Required(ErrorMessage = "رقم الفاتورة مطلوب")]
        public long InvoiceNo { get; set; }

        [Required(ErrorMessage = "رقم السطر مطلوب")]
        public int LineSN { get; set; }
    }

    public class CompleteInvoiceViewModel
    {
        [Required(ErrorMessage = "رقم الفاتورة مطلوب")]
        public long InvoiceNo { get; set; }

        [Required(ErrorMessage = "مبلغ النقد مطلوب")]
        [Range(0, double.MaxValue, ErrorMessage = "مبلغ النقد يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal CashAmount { get; set; } = 0;

        [Required(ErrorMessage = "مبلغ البطاقة مطلوب")]
        [Range(0, double.MaxValue, ErrorMessage = "مبلغ البطاقة يجب أن يكون أكبر من أو يساوي صفر")]
        public decimal CardAmount { get; set; } = 0;

        [Required(ErrorMessage = "اسم العميل مطلوب")]
        public string CustomerName { get; set; } = string.Empty;

        public string CustomerPhone { get; set; } = string.Empty;

        public long? CustomerId { get; set; }

        [Range(0, 100, ErrorMessage = "نسبة الخصم يجب أن تكون بين 0 و 100")]
        public decimal DiscountPercentage { get; set; } = 0;
    }

    public class SearchItemViewModel
    {
        [Required(ErrorMessage = "كلمة البحث مطلوبة")]
        [StringLength(100, ErrorMessage = "كلمة البحث يجب أن تكون أقل من 100 حرف")]
        public string SearchTerm { get; set; } = string.Empty;
    }

    public class BarcodeScanViewModel
    {
        [Required(ErrorMessage = "الباركود مطلوب")]
        [StringLength(100, ErrorMessage = "الباركود يجب أن يكون أقل من 100 حرف")]
        public string Barcode { get; set; } = string.Empty;

        public long InvoiceNo { get; set; }
        public string Store { get; set; } = string.Empty;
    }

    public class BarcodeProcessViewModel
    {
        [Required(ErrorMessage = "الباركود مطلوب")]
        [StringLength(100, ErrorMessage = "الباركود يجب أن يكون أقل من 100 حرف")]
        public string Barcode { get; set; } = string.Empty;

        public long InvoiceNo { get; set; }
        public string Store { get; set; } = string.Empty;
    }

    public class POSSettingsViewModel
    {
        public string DefaultPrinter { get; set; } = string.Empty;
        public bool PriceIncludeVAT { get; set; } = false;
        public bool NonVATInvoice { get; set; } = false;
        public bool ReferenceMandatory { get; set; } = false;
        public bool MandatoryCustomerVATReg { get; set; } = false;
        public string PaymentType { get; set; } = "نقدي";
        public int PrintOption { get; set; } = 0; // 0=Auto, 1=Ask
    }

    public class POSPaymentViewModel
    {
        public long InvoiceNo { get; set; }
        public decimal TotalAmount { get; set; } = 0;
        public decimal CashAmount { get; set; } = 0;
        public decimal CardAmount { get; set; } = 0;
        public decimal RemainingAmount { get; set; } = 0;
        public string CustomerName { get; set; } = string.Empty;
        public string CustomerPhone { get; set; } = string.Empty;
        public long? CustomerId { get; set; }
        public decimal DiscountPercentage { get; set; } = 0;
        public decimal DiscountAmount { get; set; } = 0;
        public decimal VATAmount { get; set; } = 0;
        public decimal NetAmount { get; set; } = 0;
    }

    public class POSReceiptViewModel
    {
        public long InvoiceNo { get; set; }
        public DateTime InvoiceDate { get; set; }
        public string Store { get; set; } = string.Empty;
        public string Cashier { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public string CustomerPhone { get; set; } = string.Empty;
        public List<POSInvoiceItemViewModel> Items { get; set; } = new List<POSInvoiceItemViewModel>();
        public decimal SubTotal { get; set; } = 0;
        public decimal VATAmount { get; set; } = 0;
        public decimal DiscountAmount { get; set; } = 0;
        public decimal TotalAmount { get; set; } = 0;
        public decimal CashAmount { get; set; } = 0;
        public decimal CardAmount { get; set; } = 0;
        public decimal ChangeAmount { get; set; } = 0;
        public byte[]? QRCodeImage { get; set; }
    }

    public class POSCreateViewModel
    {
        public List<Item> Items { get; set; } = new List<Item>();
        public List<Store> Stores { get; set; } = new List<Store>();
        public List<ChartOfAccount> Customers { get; set; } = new List<ChartOfAccount>();
        public InvoiceToolSetting Settings { get; set; } = new InvoiceToolSetting();
        public POSInvoice? Invoice { get; set; }
    }
} 