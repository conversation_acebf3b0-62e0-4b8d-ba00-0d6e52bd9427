using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using AccountingSystem.Services;

namespace AccountingSystem.Web.Controllers
{
    [Authorize]
    public class SalesController : Controller
    {
        private readonly IPOSService _posService;
        public SalesController(IPOSService posService)
        {
            _posService = posService;
        }

        public IActionResult Index()
        {
            return View();
        }

        public IActionResult Invoice()
        {
            ViewBag.PageTitle = "فواتير المبيعات";
            ViewBag.Message = "صفحة فواتير المبيعات - قيد التطوير";
            return View();
        }

        public IActionResult Return()
        {
            ViewBag.PageTitle = "مرتجع المبيعات";
            ViewBag.Message = "صفحة مرتجع المبيعات - قيد التطوير";
            return View();
        }

        public IActionResult Create()
        {
            ViewBag.PageTitle = "إنشاء فاتورة مبيعات جديدة";
            return View();
        }

        public IActionResult Edit(int id)
        {
            ViewBag.PageTitle = "تعديل فاتورة المبيعات";
            ViewBag.InvoiceId = id;
            return View();
        }

        public IActionResult Details(int id)
        {
            ViewBag.PageTitle = "تفاصيل فاتورة المبيعات";
            ViewBag.InvoiceId = id;
            return View();
        }

        [HttpGet]
        public async Task<IActionResult> Search(string? invoiceNo = null, string? customer = null, string? user = null, string? warehouse = null, DateTime? date = null)
        {
            var invoices = await _posService.SearchInvoicesAsync(invoiceNo, customer, user, warehouse, date);
            ViewBag.InvoiceNo = invoiceNo;
            ViewBag.Customer = customer;
            ViewBag.User = user;
            ViewBag.Warehouse = warehouse;
            ViewBag.Date = date;
            ViewBag.TotalSales = invoices.Sum(i => i.TrxNetAmount);
            return View(invoices);
        }
    }
}
