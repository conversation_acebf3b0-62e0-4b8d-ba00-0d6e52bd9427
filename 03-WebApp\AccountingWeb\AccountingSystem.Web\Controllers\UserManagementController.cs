using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Data.SqlClient;
using AccountingSystem.Web.Models;
using AccountingSystem.Web.Utilities;
using System.Security.Claims;

namespace AccountingSystem.Web.Controllers
{
    [Authorize]
    public class UserManagementController : Controller
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<UserManagementController> _logger;

        public UserManagementController(IConfiguration configuration, ILogger<UserManagementController> logger)
        {
            _configuration = configuration;
            _logger = logger;
        }

        /// <summary>
        /// Main user management page
        /// </summary>
        public async Task<IActionResult> Index()
        {
            // Check if user has admin privileges
            if (!await IsUserAdminAsync())
            {
                TempData["Error"] = "ليس لديك صلاحية للوصول إلى إدارة المستخدمين";
                return RedirectToAction("Index", "SimpleDashboard");
            }

            try
            {
                var viewModel = new UserManagementViewModel();
                
                // Load all data needed for the page
                viewModel.Users = await GetUsersAsync();
                viewModel.Groups = await GetGroupsAsync();
                viewModel.Stores = await GetStoresAsync();
                viewModel.Customers = await GetCustomersAsync();
                viewModel.Cashiers = await GetCashiersAsync();

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading user management page");
                TempData["Error"] = "حدث خطأ أثناء تحميل صفحة إدارة المستخدمين";
                return RedirectToAction("Index", "SimpleDashboard");
            }
        }

        /// <summary>
        /// Create new user
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> CreateUser(CreateUserViewModel model)
        {
            if (!await IsUserAdminAsync())
            {
                TempData["Error"] = "ليس لديك صلاحية لإنشاء المستخدمين";
                return RedirectToAction("Index");
            }

            if (!ModelState.IsValid)
            {
                TempData["Error"] = "يرجى التحقق من البيانات المدخلة";
                return RedirectToAction("Index");
            }

            try
            {
                var connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    // Check if username already exists
                    var checkQuery = "SELECT COUNT(*) FROM tblUsers WHERE Username = @Username";
                    using (var checkCommand = new SqlCommand(checkQuery, connection))
                    {
                        checkCommand.Parameters.AddWithValue("@Username", model.Username.Trim());
                        var exists = (int)await checkCommand.ExecuteScalarAsync() > 0;
                        
                        if (exists)
                        {
                            TempData["Error"] = "عفوًا اسم المستخدم موجود مسبقًا";
                            return RedirectToAction("Index");
                        }
                    }

                    // Create new user with VB.NET compatible password hash
                    var insertQuery = @"
                        INSERT INTO [tblUsers] 
                        ([Username], [Password], [GroupID], [DefaultStore], [StoreChange], 
                         [DefaultCustomer], [CustomerChange], [DefaultCashier], [CashierChange],
                         [ChangeInvoicePrice], [MaxDiscountPercent], [CreatedBy], [CreatedOn]) 
                        VALUES 
                        (@Username, @Password, @GroupID, @DefaultStore, @StoreChange, 
                         @DefaultCustomer, @CustomerChange, @DefaultCashier, @CashierChange,
                         @ChangeInvoicePrice, @MaxDiscountPercent, @CreatedBy, GETDATE())";

                    using (var insertCommand = new SqlCommand(insertQuery, connection))
                    {
                        // Use VB.NET compatible hash for new users to maintain compatibility
                        var hashedPassword = VBNetHashCompatibility.HashPasswordForVBNet(model.Password);
                        var currentUser = User.Identity?.Name ?? "System";

                        insertCommand.Parameters.AddWithValue("@Username", model.Username.Trim());
                        insertCommand.Parameters.AddWithValue("@Password", hashedPassword);
                        insertCommand.Parameters.AddWithValue("@GroupID", model.GroupID);
                        insertCommand.Parameters.AddWithValue("@DefaultStore", (object?)model.DefaultStore ?? DBNull.Value);
                        insertCommand.Parameters.AddWithValue("@StoreChange", model.StoreChange);
                        insertCommand.Parameters.AddWithValue("@DefaultCustomer", (object?)model.DefaultCustomer ?? DBNull.Value);
                        insertCommand.Parameters.AddWithValue("@CustomerChange", model.CustomerChange);
                        insertCommand.Parameters.AddWithValue("@DefaultCashier", (object?)model.DefaultCashier ?? DBNull.Value);
                        insertCommand.Parameters.AddWithValue("@CashierChange", model.CashierChange);
                        insertCommand.Parameters.AddWithValue("@ChangeInvoicePrice", model.ChangeInvoicePrice);
                        insertCommand.Parameters.AddWithValue("@MaxDiscountPercent", (object?)model.MaxDiscountPercent ?? DBNull.Value);
                        insertCommand.Parameters.AddWithValue("@CreatedBy", currentUser);

                        await insertCommand.ExecuteNonQueryAsync();
                    }
                }

                _logger.LogInformation("User {Username} created successfully by {CreatedBy}", model.Username, User.Identity?.Name);
                TempData["Success"] = "تم إنشاء المستخدم بنجاح";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user {Username}", model.Username);
                TempData["Error"] = "حدث خطأ أثناء إنشاء المستخدم";
            }

            return RedirectToAction("Index");
        }

        /// <summary>
        /// Edit existing user
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> EditUser(EditUserViewModel model)
        {
            if (!await IsUserAdminAsync())
            {
                TempData["Error"] = "ليس لديك صلاحية لتعديل المستخدمين";
                return RedirectToAction("Index");
            }

            if (!ModelState.IsValid)
            {
                TempData["Error"] = "يرجى التحقق من البيانات المدخلة";
                return RedirectToAction("Index");
            }

            try
            {
                var connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    var updateQuery = @"
                        UPDATE tblUsers SET 
                            GroupID = @GroupID, 
                            DefaultStore = @DefaultStore, 
                            StoreChange = @StoreChange, 
                            DefaultCustomer = @DefaultCustomer, 
                            CustomerChange = @CustomerChange, 
                            DefaultCashier = @DefaultCashier, 
                            CashierChange = @CashierChange,
                            ChangeInvoicePrice = @ChangeInvoicePrice,
                            MaxDiscountPercent = @MaxDiscountPercent, 
                            ModifiedBy = @ModifiedBy, 
                            ModifiedOn = GETDATE() 
                        WHERE SN = @SN AND Username <> 'admin'";

                    using (var updateCommand = new SqlCommand(updateQuery, connection))
                    {
                        var currentUser = User.Identity?.Name ?? "System";

                        updateCommand.Parameters.AddWithValue("@SN", model.SN);
                        updateCommand.Parameters.AddWithValue("@GroupID", model.GroupID);
                        updateCommand.Parameters.AddWithValue("@DefaultStore", (object?)model.DefaultStore ?? DBNull.Value);
                        updateCommand.Parameters.AddWithValue("@StoreChange", model.StoreChange);
                        updateCommand.Parameters.AddWithValue("@DefaultCustomer", (object?)model.DefaultCustomer ?? DBNull.Value);
                        updateCommand.Parameters.AddWithValue("@CustomerChange", model.CustomerChange);
                        updateCommand.Parameters.AddWithValue("@DefaultCashier", (object?)model.DefaultCashier ?? DBNull.Value);
                        updateCommand.Parameters.AddWithValue("@CashierChange", model.CashierChange);
                        updateCommand.Parameters.AddWithValue("@ChangeInvoicePrice", model.ChangeInvoicePrice);
                        updateCommand.Parameters.AddWithValue("@MaxDiscountPercent", (object?)model.MaxDiscountPercent ?? DBNull.Value);
                        updateCommand.Parameters.AddWithValue("@ModifiedBy", currentUser);

                        var rowsAffected = await updateCommand.ExecuteNonQueryAsync();
                        
                        if (rowsAffected == 0)
                        {
                            TempData["Error"] = "لم يتم العثور على المستخدم أو لا يمكن تعديله";
                            return RedirectToAction("Index");
                        }
                    }
                }

                _logger.LogInformation("User {Username} updated successfully by {UpdatedBy}", model.Username, User.Identity?.Name);
                TempData["Success"] = "تم تحديث المستخدم بنجاح";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user {Username}", model.Username);
                TempData["Error"] = "حدث خطأ أثناء تحديث المستخدم";
            }

            return RedirectToAction("Index");
        }

        /// <summary>
        /// Change user password
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ChangePassword(ChangePasswordViewModel model)
        {
            if (!ModelState.IsValid)
            {
                TempData["Error"] = "يرجى التحقق من البيانات المدخلة";
                return RedirectToAction("Index");
            }

            try
            {
                var connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    // Check if user exists
                    var checkQuery = "SELECT SN FROM tblUsers WHERE Username = @Username";
                    using (var checkCommand = new SqlCommand(checkQuery, connection))
                    {
                        checkCommand.Parameters.AddWithValue("@Username", model.Username.Trim());
                        var userSN = await checkCommand.ExecuteScalarAsync();
                        
                        if (userSN == null)
                        {
                            TempData["Error"] = "اسم المستخدم غير موجود";
                            return RedirectToAction("Index");
                        }

                        // Update password with C# hash (new passwords will be C# compatible)
                        var updateQuery = @"
                            UPDATE tblUsers SET 
                                Password = @Password,
                                ModifiedBy = @ModifiedBy,
                                ModifiedOn = GETDATE()
                            WHERE Username = @Username AND Username <> 'admin'";

                        using (var updateCommand = new SqlCommand(updateQuery, connection))
                        {
                            var hashedPassword = VBNetHashCompatibility.HashPasswordForVBNet(model.NewPassword);
                            var currentUser = User.Identity?.Name ?? "System";

                            updateCommand.Parameters.AddWithValue("@Username", model.Username.Trim());
                            updateCommand.Parameters.AddWithValue("@Password", hashedPassword);
                            updateCommand.Parameters.AddWithValue("@ModifiedBy", currentUser);

                            var rowsAffected = await updateCommand.ExecuteNonQueryAsync();
                            
                            if (rowsAffected == 0)
                            {
                                TempData["Error"] = "لا يمكن تغيير كلمة مرور هذا المستخدم";
                                return RedirectToAction("Index");
                            }
                        }
                    }
                }

                _logger.LogInformation("Password changed for user {Username} by {ChangedBy}", model.Username, User.Identity?.Name);
                TempData["Success"] = "تم تغيير كلمة المرور بنجاح";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error changing password for user {Username}", model.Username);
                TempData["Error"] = "حدث خطأ أثناء تغيير كلمة المرور";
            }

            return RedirectToAction("Index");
        }

        /// <summary>
        /// Delete user
        /// </summary>
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteUser(string username)
        {
            if (string.IsNullOrWhiteSpace(username))
            {
                TempData["Error"] = "اسم المستخدم مطلوب";
                return RedirectToAction("Index");
            }

            // Prevent deleting current user
            if (username.Equals(User.Identity?.Name, StringComparison.OrdinalIgnoreCase))
            {
                TempData["Error"] = "لا يمكن حذف المستخدم الحالي";
                return RedirectToAction("Index");
            }

            try
            {
                var connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    var deleteQuery = "DELETE FROM tblUsers WHERE Username = @Username AND Username <> 'admin'";
                    using (var deleteCommand = new SqlCommand(deleteQuery, connection))
                    {
                        deleteCommand.Parameters.AddWithValue("@Username", username.Trim());
                        var rowsAffected = await deleteCommand.ExecuteNonQueryAsync();

                        if (rowsAffected == 0)
                        {
                            TempData["Error"] = "لم يتم العثور على المستخدم أو لا يمكن حذفه";
                            return RedirectToAction("Index");
                        }
                    }
                }

                _logger.LogInformation("User {Username} deleted by {DeletedBy}", username, User.Identity?.Name);
                TempData["Success"] = "تم حذف المستخدم بنجاح";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user {Username}", username);
                TempData["Error"] = "حدث خطأ أثناء حذف المستخدم";
            }

            return RedirectToAction("Index");
        }

        #region Private Helper Methods

        private async Task<List<UserViewModel>> GetUsersAsync()
        {
            var users = new List<UserViewModel>();
            var connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (var connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                var query = @"
                    SELECT u.SN, u.Username, u.GroupID, g.GroupName
                    FROM tblUsers u
                    LEFT JOIN tblGroupsAuth g ON u.GroupID = g.GroupID
                    WHERE u.Username <> 'admin'
                    ORDER BY u.Username";

                using (var command = new SqlCommand(query, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            users.Add(new UserViewModel
                            {
                                SN = Convert.ToInt32(reader["SN"]),
                                Username = reader["Username"].ToString() ?? "",
                                GroupID = Convert.ToInt32(reader["GroupID"]),
                                GroupName = reader["GroupName"]?.ToString() ?? "",
                                // Set default values for fields that may not exist in database yet
                                DefaultStore = null,
                                StoreChange = false,
                                DefaultCustomer = null,
                                CustomerChange = false,
                                DefaultCashier = null,
                                CashierChange = false,
                                ChangeInvoicePrice = false,
                                MaxDiscountPercent = null,
                                CreatedOn = null,
                                CreatedBy = null,
                                ModifiedOn = null,
                                ModifiedBy = null
                            });
                        }
                    }
                }
            }

            return users;
        }

        private async Task<List<AccountingSystem.Web.Models.UserGroupViewModel>> GetGroupsAsync()
        {
            var groups = new List<AccountingSystem.Web.Models.UserGroupViewModel>();
            var connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (var connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                var query = "SELECT GroupID, GroupName FROM tblGroupsAuth ORDER BY GroupName";
                using (var command = new SqlCommand(query, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            groups.Add(new AccountingSystem.Web.Models.UserGroupViewModel
                            {
                                GroupID = Convert.ToInt32(reader["GroupID"]),
                                GroupName = reader["GroupName"]?.ToString() ?? ""
                            });
                        }
                    }
                }
            }

            return groups;
        }

        private async Task<List<DropdownOptionViewModel>> GetStoresAsync()
        {
            var stores = new List<DropdownOptionViewModel>();
            var connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (var connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                var query = "SELECT Store FROM tblStores ORDER BY Store";
                using (var command = new SqlCommand(query, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            var store = reader["Store"]?.ToString() ?? "";
                            stores.Add(new DropdownOptionViewModel
                            {
                                Value = store,
                                Text = store
                            });
                        }
                    }
                }
            }

            return stores;
        }

        private async Task<List<DropdownOptionViewModel>> GetCustomersAsync()
        {
            var customers = new List<DropdownOptionViewModel>();
            var connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (var connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                // Assuming there's a customers table - adjust query as needed
                var query = "SELECT TOP 100 CustomerName FROM tblCustomers ORDER BY CustomerName";
                using (var command = new SqlCommand(query, connection))
                {
                    try
                    {
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var customer = reader["CustomerName"]?.ToString() ?? "";
                                customers.Add(new DropdownOptionViewModel
                                {
                                    Value = customer,
                                    Text = customer
                                });
                            }
                        }
                    }
                    catch
                    {
                        // Table might not exist, return empty list
                    }
                }
            }

            return customers;
        }

        private async Task<List<DropdownOptionViewModel>> GetCashiersAsync()
        {
            var cashiers = new List<DropdownOptionViewModel>();
            var connectionString = _configuration.GetConnectionString("DefaultConnection");

            using (var connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync();

                // Assuming there's a cashiers table - adjust query as needed
                var query = "SELECT CashierName FROM tblCashiers ORDER BY CashierName";
                using (var command = new SqlCommand(query, connection))
                {
                    try
                    {
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var cashier = reader["CashierName"]?.ToString() ?? "";
                                cashiers.Add(new DropdownOptionViewModel
                                {
                                    Value = cashier,
                                    Text = cashier
                                });
                            }
                        }
                    }
                    catch
                    {
                        // Table might not exist, return empty list
                    }
                }
            }

            return cashiers;
        }

        /// <summary>
        /// Check if the current user has admin privileges
        /// </summary>
        private async Task<bool> IsUserAdminAsync()
        {
            var currentUsername = User.Identity?.Name;
            if (string.IsNullOrEmpty(currentUsername))
                return false;

            try
            {
                var connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        SELECT g.GroupName
                        FROM tblUsers u
                        INNER JOIN tblGroupsAuth g ON u.GroupID = g.GroupID
                        WHERE u.Username = @Username";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Username", currentUsername);
                        var groupName = await command.ExecuteScalarAsync() as string;

                        // Check if user is in admin group (case-insensitive)
                        return !string.IsNullOrEmpty(groupName) &&
                               groupName.Equals("admin", StringComparison.OrdinalIgnoreCase);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking admin privileges for user {Username}", currentUsername);
                return false;
            }
        }

        /// <summary>
        /// Get user details for editing (AJAX endpoint)
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetUserDetails(int sn)
        {
            try
            {
                // Check if user has admin privileges
                if (!await IsUserAdminAsync())
                {
                    return Json(new { error = "ليس لديك صلاحية للوصول إلى بيانات المستخدمين" });
                }

                var connectionString = _configuration.GetConnectionString("DefaultConnection");
                using (var connection = new SqlConnection(connectionString))
                {
                    await connection.OpenAsync();

                    var query = @"
                        SELECT u.SN, u.Username, u.GroupID, g.GroupName
                        FROM tblUsers u
                        LEFT JOIN tblGroupsAuth g ON u.GroupID = g.GroupID
                        WHERE u.SN = @SN AND u.Username <> 'admin'";

                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@SN", sn);

                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            if (await reader.ReadAsync())
                            {
                                var user = new
                                {
                                    sn = reader.IsDBNull(0) ? 0 : reader.GetInt32(0),
                                    username = reader.IsDBNull(1) ? "" : reader.GetString(1),
                                    groupID = reader.IsDBNull(2) ? 0 : reader.GetInt32(2),
                                    groupName = reader.IsDBNull(3) ? "" : reader.GetString(3),
                                    // Set default values for fields that may not exist in database yet
                                    defaultStore = "",
                                    storeChange = false,
                                    defaultCustomer = "",
                                    customerChange = false,
                                    defaultCashier = "",
                                    cashierChange = false,
                                    changeInvoicePrice = false,
                                    maxDiscountPercent = (decimal?)null
                                };

                                return Json(user);
                            }
                            else
                            {
                                return Json(new { error = "المستخدم غير موجود أو لا يمكن تعديله" });
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user details for SN: {SN}", sn);
                return Json(new { error = "حدث خطأ أثناء جلب بيانات المستخدم" });
            }
        }



        #endregion
    }
}
